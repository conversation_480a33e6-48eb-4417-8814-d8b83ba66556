{"$id": "https://www.visymo.com/group_component_layout.schema.json", "$schema": "http://json-schema.org/draft-07/schema#", "additionalProperties": false, "type": "object", "definitions": {"property": {"property1": {"type": "string", "default": "default", "enum": ["default"]}, "property2": {"type": ["integer", "null"], "default": null, "enum": [1, 2, 4, 5, 6, 7]}, "property3": {"type": "array", "default": [], "items": {"type": "string", "enum": ["bottom", "bottom-5l", "bottom-l", "bottom-xl", "bottom-xxl", "top", "top-l", "top-xl", "top-xxl"]}}, "property4": {"type": "string", "default": "default", "enum": ["default", "dsrw", "image-search", "info"]}, "property5": {"type": ["string", "null"], "default": null, "enum": ["one", "three", "two"]}, "property6": {"type": "array", "default": [], "items": {"type": "string", "enum": ["background", "border-top", "box-shadow", "color"]}}, "property7": {"type": "string", "default": "center", "enum": ["center", "left", "right"]}, "property8": {"type": "string", "default": "default", "enum": ["default", "header", "large"]}, "property9": {"type": "string", "default": "default", "enum": ["container", "default"]}, "property10": {"type": "string", "default": "card-1", "enum": ["card-1", "card-10", "card-2", "card-3", "card-4", "card-5", "card-6", "card-7", "card-8", "card-9", "list-1", "list-2", "rank-list-1", "rank-list-2"]}, "property11": {"type": "string", "default": "default", "enum": ["dark", "default", "dsr", "dsr-dark", "<PERSON><PERSON>", "seekweb", "visymo", "zapmeta"]}, "property12": {"type": "string", "default": "default", "enum": ["default", "small"]}, "property13": {"type": "string", "default": "default", "enum": ["content", "default", "title"]}, "property14": {"type": "string", "enum": ["bottom", "sidebar", "top"]}, "property15": {"type": ["string", "null"], "default": null, "enum": ["color-white"]}, "property16": {"type": "string", "default": "default", "enum": ["borderless", "brand", "content-1", "content-3", "content-4", "default", "dsr", "seekweb", "visymo-search"]}, "property17": {"type": "string", "enum": ["content", "content_without_terms", "search"]}, "property18": {"type": "string", "enum": ["search_footer", "search_header", "search_home_footer", "search_home_header", "search_organic_pagination"]}, "property19": {"type": "string", "default": "header-dark.jpg", "enum": ["header-dark.jpg", "header-light.jpg"]}, "property20": {"type": "string", "default": null, "enum": ["about", "add_remove_site", "advanced_search", "advertising", "company_information", "frequently_asked_questions", "platforms", "search_components", "search_multiple_sources", "useful_search_tips"]}, "property21": {"type": "string", "default": "default", "enum": ["content", "default"]}, "property22": {"type": "string", "default": "default", "enum": ["default", "seekweb"]}, "property23": {"type": "string", "default": "default", "enum": ["dark", "default", "dsr", "dsr-dark"]}, "property24": {"type": "string", "default": "default", "enum": ["default", "modern", "rounded"]}, "property25": {"type": "array", "default": ["top"], "items": {"type": "string", "enum": ["bottom", "bottom-5l", "bottom-l", "bottom-xl", "bottom-xxl", "top", "top-l", "top-xl", "top-xxl"]}}, "property26": {"type": "string", "default": "default", "enum": ["dark", "default"]}, "property27": {"type": "string", "default": "default", "enum": ["default", "stacked"]}, "property28": {"type": "string", "default": "default", "enum": ["chevron", "compact", "compact-blue", "compact-light", "compact-zapmeta", "default", "fallback", "fallback-brand", "fallback-dark", "pill", "seekweb", "visymo", "zapmeta"]}, "property29": {"type": ["string", "null"], "default": null, "enum": ["b", "i", "r"]}, "property30": {"type": "string", "default": "default", "enum": ["default", "dsrw"]}, "property31": {"type": "string", "default": "default", "enum": ["content", "content-1", "content-category-1", "content-category-2", "default", "home", "home_rounded", "rounded", "startpage", "visymo"]}, "property32": {"type": "string", "enum": ["content_page"]}, "property33": {"type": "string", "default": "default", "enum": ["compact", "default", "seekweb"]}, "property34": {"type": "string", "default": "slogan.search_and_combine_all_search_engines", "enum": ["about_us.content", "about_us.title", "advanced_search.form.search_all.label", "advanced_search.form.search_at_least_one.label", "advanced_search.form.search_exact_phrase.label", "advanced_search.form.search_site.description", "advanced_search.form.search_site.label", "advanced_search.form.search_without.label", "advanced_search.form.submit.label", "advanced_search.title", "alt_search.result.published_by", "collapse_children.read_more", "contact.description.form", "contact.form.subject.placeholder", "contact.form.thank_you", "content_page.all_articles", "content_page.categories.title", "content_page.categories.title_highlight", "content_page.continue_reading", "content_page.disclaimer", "content_page.editor_choice", "content_page.editor_choice_highlight", "content_page.entire_article_collection", "content_page.explore_latest.title", "content_page.explore_latest.title_subtitle", "content_page.explore_more", "content_page.from_the_editors.title", "content_page.from_the_editors.title_subtitle", "content_page.home.title", "content_page.latest", "content_page.latest_article", "content_page.min_read", "content_page.more_articles.title", "content_page.more_articles.title_highlight", "content_page.more_top_stories.title", "content_page.more_top_stories.title_highlight", "content_page.more_topics.title", "content_page.popular_articles", "content_page.published_on", "content_page.read", "content_page.read_more", "content_page.recent_articles", "content_page.recent_releases", "content_page.subtitle", "content_page.table_of_contents", "content_page.title", "content_page.title_highlight", "content_page.top_stories.title", "content_page.top_stories.title_highlight", "content_page.trending_topics.title", "content_page.updated_articles", "content_page_footer.highlight", "content_page_footer.text", "content_page_footer.written_by", "content_page_footer.written_by_editorial", "cookie_consent.accept", "cookie_consent.message", "cookie_consent.more_info", "cookie_consent.title", "disclaimer.text", "discover.title", "error_page.message", "error_page.page_not_found.message", "error_page.page_not_found.title", "error_page.title", "footer.about_us", "footer.all_rights_reserved", "footer.contact", "footer.cookie_settings", "footer.copyright", "footer.disclaimer", "footer.privacy", "image_search.color.item.black", "image_search.color.item.blue", "image_search.color.item.brown", "image_search.color.item.gray", "image_search.color.item.green", "image_search.color.item.orange", "image_search.color.item.pink", "image_search.color.item.purple", "image_search.color.item.red", "image_search.color.item.teal", "image_search.color.item.white", "image_search.color.item.yellow", "image_search.color.label", "image_search.home.slogan", "image_search.image_size.item.large", "image_search.image_size.item.medium", "image_search.image_size.item.small", "image_search.image_size.item.wallpaper", "image_search.image_size.label", "image_search.image_type.item.clipart", "image_search.image_type.item.gif", "image_search.image_type.item.line", "image_search.image_type.item.photo", "image_search.image_type.item.transparent", "image_search.image_type.label", "image_search.period.item.day", "image_search.period.item.month", "image_search.period.item.week", "image_search.period.label", "image_search.title", "info_page.about.content", "info_page.about.our_mission.content", "info_page.about.our_mission.title", "info_page.about.our_vision.content", "info_page.about.our_vision.title", "info_page.about.part_of_visymo", "info_page.about.title", "info_page.about.what_we_offer.in_depth_articles.content", "info_page.about.what_we_offer.in_depth_articles.title", "info_page.about.what_we_offer.search_results.content", "info_page.about.what_we_offer.search_results.title", "info_page.about.what_we_offer.title", "info_page.about.what_we_offer.user_experience.content", "info_page.about.what_we_offer.user_experience.title", "info_page.about.who_we_are.content", "info_page.about.who_we_are.title", "menu.advanced_search", "menu.images", "menu.more", "menu.news", "menu.settings", "menu.shopping", "menu.web", "meta.description", "meta.description.display", "news_search.category.item.business", "news_search.category.item.entertainment", "news_search.category.item.politics", "news_search.category.item.sports", "news_search.category.item.world", "news_search.category.label", "news_search.home.slogan", "news_search.period.item.day", "news_search.period.item.month", "news_search.period.item.week", "news_search.period.label", "news_search.title", "organic.more", "organic.title", "organic.title_query", "page_navigation.link.add_remove_site", "page_navigation.link.advanced_search", "page_navigation.link.advertising", "page_navigation.link.company_information", "page_navigation.link.contact", "page_navigation.link.cookie", "page_navigation.link.copyright", "page_navigation.link.disclaimer", "page_navigation.link.frequently_asked_questions", "page_navigation.link.platforms", "page_navigation.link.power_of", "page_navigation.link.privacy", "page_navigation.link.search_components", "page_navigation.link.search_multiple_sources", "page_navigation.link.useful_search_tips", "page_navigation.section.general", "page_navigation.section.search", "page_navigation.section.webmasters", "pagination.more", "pagination.next", "pagination.page", "pagination.previous", "preferences.form.keyword_highlight.description", "preferences.form.keyword_highlight.label", "preferences.form.previous_searches.description", "preferences.form.previous_searches.label", "preferences.form.query_suggestion.description", "preferences.form.query_suggestion.label", "preferences.form.safe_search.description", "preferences.form.safe_search.label", "preferences.form.same_window.label", "preferences.form.submit.label", "preferences.title", "related_terms.display.title", "related_terms.short_title", "related_terms.title", "search.process_time", "search_error.fatal_error.message", "search_error.fatal_error.title", "search_error.no_results.check_spelling", "search_error.no_results.different_query", "search_error.no_results.explore_related_searches", "search_error.no_results.message", "search_error.no_results.title", "search_field.erase_history_label", "search_field.search", "search_header.more_categories", "search_results.title.page_stats", "share_page.share_now", "slogan.search_and_combine_all_search_engines", "slogan.source"]}, "property35": {"type": "string", "default": "default", "enum": ["bar", "content-1", "content-2", "content-3", "content-4", "content-5", "default", "display", "dsr", "dsr-dark", "header"]}, "property36": {"type": ["string", "null"], "default": null, "enum": ["about_us.content", "about_us.title", "advanced_search.form.search_all.label", "advanced_search.form.search_at_least_one.label", "advanced_search.form.search_exact_phrase.label", "advanced_search.form.search_site.description", "advanced_search.form.search_site.label", "advanced_search.form.search_without.label", "advanced_search.form.submit.label", "advanced_search.title", "alt_search.result.published_by", "collapse_children.read_more", "contact.description.form", "contact.form.subject.placeholder", "contact.form.thank_you", "content_page.all_articles", "content_page.categories.title", "content_page.categories.title_highlight", "content_page.continue_reading", "content_page.disclaimer", "content_page.editor_choice", "content_page.editor_choice_highlight", "content_page.entire_article_collection", "content_page.explore_latest.title", "content_page.explore_latest.title_subtitle", "content_page.explore_more", "content_page.from_the_editors.title", "content_page.from_the_editors.title_subtitle", "content_page.home.title", "content_page.latest", "content_page.latest_article", "content_page.min_read", "content_page.more_articles.title", "content_page.more_articles.title_highlight", "content_page.more_top_stories.title", "content_page.more_top_stories.title_highlight", "content_page.more_topics.title", "content_page.popular_articles", "content_page.published_on", "content_page.read", "content_page.read_more", "content_page.recent_articles", "content_page.recent_releases", "content_page.subtitle", "content_page.table_of_contents", "content_page.title", "content_page.title_highlight", "content_page.top_stories.title", "content_page.top_stories.title_highlight", "content_page.trending_topics.title", "content_page.updated_articles", "content_page_footer.highlight", "content_page_footer.text", "content_page_footer.written_by", "content_page_footer.written_by_editorial", "cookie_consent.accept", "cookie_consent.message", "cookie_consent.more_info", "cookie_consent.title", "disclaimer.text", "discover.title", "error_page.message", "error_page.page_not_found.message", "error_page.page_not_found.title", "error_page.title", "footer.about_us", "footer.all_rights_reserved", "footer.contact", "footer.cookie_settings", "footer.copyright", "footer.disclaimer", "footer.privacy", "image_search.color.item.black", "image_search.color.item.blue", "image_search.color.item.brown", "image_search.color.item.gray", "image_search.color.item.green", "image_search.color.item.orange", "image_search.color.item.pink", "image_search.color.item.purple", "image_search.color.item.red", "image_search.color.item.teal", "image_search.color.item.white", "image_search.color.item.yellow", "image_search.color.label", "image_search.home.slogan", "image_search.image_size.item.large", "image_search.image_size.item.medium", "image_search.image_size.item.small", "image_search.image_size.item.wallpaper", "image_search.image_size.label", "image_search.image_type.item.clipart", "image_search.image_type.item.gif", "image_search.image_type.item.line", "image_search.image_type.item.photo", "image_search.image_type.item.transparent", "image_search.image_type.label", "image_search.period.item.day", "image_search.period.item.month", "image_search.period.item.week", "image_search.period.label", "image_search.title", "info_page.about.content", "info_page.about.our_mission.content", "info_page.about.our_mission.title", "info_page.about.our_vision.content", "info_page.about.our_vision.title", "info_page.about.part_of_visymo", "info_page.about.title", "info_page.about.what_we_offer.in_depth_articles.content", "info_page.about.what_we_offer.in_depth_articles.title", "info_page.about.what_we_offer.search_results.content", "info_page.about.what_we_offer.search_results.title", "info_page.about.what_we_offer.title", "info_page.about.what_we_offer.user_experience.content", "info_page.about.what_we_offer.user_experience.title", "info_page.about.who_we_are.content", "info_page.about.who_we_are.title", "menu.advanced_search", "menu.images", "menu.more", "menu.news", "menu.settings", "menu.shopping", "menu.web", "meta.description", "meta.description.display", "news_search.category.item.business", "news_search.category.item.entertainment", "news_search.category.item.politics", "news_search.category.item.sports", "news_search.category.item.world", "news_search.category.label", "news_search.home.slogan", "news_search.period.item.day", "news_search.period.item.month", "news_search.period.item.week", "news_search.period.label", "news_search.title", "organic.more", "organic.title", "organic.title_query", "page_navigation.link.add_remove_site", "page_navigation.link.advanced_search", "page_navigation.link.advertising", "page_navigation.link.company_information", "page_navigation.link.contact", "page_navigation.link.cookie", "page_navigation.link.copyright", "page_navigation.link.disclaimer", "page_navigation.link.frequently_asked_questions", "page_navigation.link.platforms", "page_navigation.link.power_of", "page_navigation.link.privacy", "page_navigation.link.search_components", "page_navigation.link.search_multiple_sources", "page_navigation.link.useful_search_tips", "page_navigation.section.general", "page_navigation.section.search", "page_navigation.section.webmasters", "pagination.more", "pagination.next", "pagination.page", "pagination.previous", "preferences.form.keyword_highlight.description", "preferences.form.keyword_highlight.label", "preferences.form.previous_searches.description", "preferences.form.previous_searches.label", "preferences.form.query_suggestion.description", "preferences.form.query_suggestion.label", "preferences.form.safe_search.description", "preferences.form.safe_search.label", "preferences.form.same_window.label", "preferences.form.submit.label", "preferences.title", "related_terms.display.title", "related_terms.short_title", "related_terms.title", "search.process_time", "search_error.fatal_error.message", "search_error.fatal_error.title", "search_error.no_results.check_spelling", "search_error.no_results.different_query", "search_error.no_results.explore_related_searches", "search_error.no_results.message", "search_error.no_results.title", "search_field.erase_history_label", "search_field.search", "search_header.more_categories", "search_results.title.page_stats", "share_page.share_now", "slogan.search_and_combine_all_search_engines", "slogan.source"]}}, "component": {"anyOf": [{"$ref": "#/definitions/components/advancedSearchComponent"}, {"$ref": "#/definitions/components/bingAdsBottomAdUnitComponent"}, {"$ref": "#/definitions/components/bingAdsSidebarAdUnitComponent"}, {"$ref": "#/definitions/components/bingAdsTopAdUnitComponent"}, {"$ref": "#/definitions/components/brandLogoComponent"}, {"$ref": "#/definitions/components/collapseChildrenComponent"}, {"$ref": "#/definitions/components/columnsComponent"}, {"$ref": "#/definitions/components/columnsRangeComponent"}, {"$ref": "#/definitions/components/contentPageComponent"}, {"$ref": "#/definitions/components/contentPageCategoryHeaderComponent"}, {"$ref": "#/definitions/components/contentPageCategoryResultsComponent"}, {"$ref": "#/definitions/components/contentPageContinueReadingComponent"}, {"$ref": "#/definitions/components/contentPageExcerptComponent"}, {"$ref": "#/definitions/components/contentPageFooterComponent"}, {"$ref": "#/definitions/components/contentPageHeaderComponent"}, {"$ref": "#/definitions/components/contentPageImageComponent"}, {"$ref": "#/definitions/components/contentPageParagraphComponent"}, {"$ref": "#/definitions/components/contentPageParagraphsComponent"}, {"$ref": "#/definitions/components/contentPageResultsComponent"}, {"$ref": "#/definitions/components/contentPageResultsAsOrganicResultsComponent"}, {"$ref": "#/definitions/components/contentPageSpotlightComponent"}, {"$ref": "#/definitions/components/contentPageTitleComponent"}, {"$ref": "#/definitions/components/currentPageMatchesComponent"}, {"$ref": "#/definitions/components/disclaimerComponent"}, {"$ref": "#/definitions/components/displayBannerComponent"}, {"$ref": "#/definitions/components/dynamicAdsComponent"}, {"$ref": "#/definitions/components/footerComponent"}, {"$ref": "#/definitions/components/footerLogoComponent"}, {"$ref": "#/definitions/components/footerNavigationComponent"}, {"$ref": "#/definitions/components/googleAdsBottomUnitComponent"}, {"$ref": "#/definitions/components/googleAdsTopUnitComponent"}, {"$ref": "#/definitions/components/googleRelatedTermsComponent"}, {"$ref": "#/definitions/components/groupComponent"}, {"$ref": "#/definitions/components/hasBingAdsComponent"}, {"$ref": "#/definitions/components/hasContentPageComponent"}, {"$ref": "#/definitions/components/hasOrganicResultsComponent"}, {"$ref": "#/definitions/components/hasRelatedTermsComponent"}, {"$ref": "#/definitions/components/imageComponent"}, {"$ref": "#/definitions/components/imageErrorMessageComponent"}, {"$ref": "#/definitions/components/imageFiltersComponent"}, {"$ref": "#/definitions/components/imageResultsComponent"}, {"$ref": "#/definitions/components/infoPageComponent"}, {"$ref": "#/definitions/components/infoPageMenuComponent"}, {"$ref": "#/definitions/components/isAdBotComponent"}, {"$ref": "#/definitions/components/jumbotronComponent"}, {"$ref": "#/definitions/components/newsErrorMessageComponent"}, {"$ref": "#/definitions/components/newsFiltersComponent"}, {"$ref": "#/definitions/components/newsResultsComponent"}, {"$ref": "#/definitions/components/organicContentPageResultsComponent"}, {"$ref": "#/definitions/components/organicErrorMessageComponent"}, {"$ref": "#/definitions/components/organicPaginationComponent"}, {"$ref": "#/definitions/components/organicResultsComponent"}, {"$ref": "#/definitions/components/organicResultsTitleComponent"}, {"$ref": "#/definitions/components/organicResultsWithFallbackComponent"}, {"$ref": "#/definitions/components/pageNotFoundErrorMessageComponent"}, {"$ref": "#/definitions/components/pillRelatedTermsComponent"}, {"$ref": "#/definitions/components/preferencesComponent"}, {"$ref": "#/definitions/components/relatedSourceMatchesVisymoComponent"}, {"$ref": "#/definitions/components/relatedTermsComponent"}, {"$ref": "#/definitions/components/scrollToTopComponent"}, {"$ref": "#/definitions/components/searchBarComponent"}, {"$ref": "#/definitions/components/searchHeaderComponent"}, {"$ref": "#/definitions/components/sharePageComponent"}, {"$ref": "#/definitions/components/sloganComponent"}, {"$ref": "#/definitions/components/splitTestMatchesComponent"}, {"$ref": "#/definitions/components/startPageComponent"}, {"$ref": "#/definitions/components/startPageFavoritesComponent"}, {"$ref": "#/definitions/components/tableOfContentsComponent"}, {"$ref": "#/definitions/components/titleComponent"}, {"$ref": "#/definitions/components/unexpectedErrorMessageComponent"}, {"$ref": "#/definitions/components/webSearchStatsTitleComponent"}]}, "components": {"advancedSearchComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"layout": {"$ref": "#/definitions/property/property1"}, "type": {"type": "string", "const": "advanced_search"}}}, "bingAdsBottomAdUnitComponent": {"additionalProperties": false, "type": "object", "required": ["amount"], "properties": {"ad_style_id": {"$ref": "#/definitions/property/property2"}, "amount": {"type": "integer", "exclusiveMinimum": 0}, "layout": {"$ref": "#/definitions/property/property1"}, "type": {"type": "string", "const": "bing_ads_bottom_ad_unit"}}}, "bingAdsSidebarAdUnitComponent": {"additionalProperties": false, "type": "object", "required": ["amount"], "properties": {"ad_style_id": {"$ref": "#/definitions/property/property2"}, "amount": {"type": "integer", "exclusiveMinimum": 0}, "layout": {"$ref": "#/definitions/property/property1"}, "type": {"type": "string", "const": "bing_ads_sidebar_ad_unit"}}}, "bingAdsTopAdUnitComponent": {"additionalProperties": false, "type": "object", "required": ["amount"], "properties": {"ad_style_id": {"$ref": "#/definitions/property/property2"}, "amount": {"type": "integer", "exclusiveMinimum": 0}, "layout": {"$ref": "#/definitions/property/property1"}, "type": {"type": "string", "const": "bing_ads_top_ad_unit"}}}, "brandLogoComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"component_space_modifiers": {"$ref": "#/definitions/property/property3"}, "layout": {"$ref": "#/definitions/property/property1"}, "link_to_home": {"type": "boolean", "default": false}, "logo_dark_mode": {"type": "boolean", "default": false}, "type": {"type": "string", "const": "brand_logo"}}}, "collapseChildrenComponent": {"additionalProperties": false, "type": "object", "required": ["children"], "properties": {"children": {"type": "array", "items": {"$ref": "#/definitions/component"}}, "layout": {"$ref": "#/definitions/property/property1"}, "type": {"type": "string", "const": "collapse_children"}}}, "columnsComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"layout": {"$ref": "#/definitions/property/property4"}, "main_column": {"$ref": "#/definitions/property/property5"}, "one": {"type": "array", "items": {"$ref": "#/definitions/component"}}, "section": {"type": ["string", "null"], "default": null, "pattern": "^([a-z][a-z0-9-]*[a-z0-9])$"}, "section_css_properties": {"$ref": "#/definitions/property/property6"}, "section_visible": {"type": "boolean", "default": true}, "three": {"type": "array", "items": {"$ref": "#/definitions/component"}}, "two": {"type": "array", "items": {"$ref": "#/definitions/component"}}, "type": {"type": "string", "const": "columns"}}}, "columnsRangeComponent": {"additionalProperties": false, "type": "object", "required": ["end", "start"], "properties": {"components": {"type": "array", "items": {"$ref": "#/definitions/component"}}, "end": {"type": "integer", "minimum": 1, "maximum": 3}, "layout": {"$ref": "#/definitions/property/property1"}, "section": {"type": ["string", "null"], "default": null, "pattern": "^([a-z][a-z0-9-]*[a-z0-9])$"}, "section_css_properties": {"$ref": "#/definitions/property/property6"}, "section_visible": {"type": "boolean", "default": true}, "start": {"type": "integer", "minimum": 1, "maximum": 3}, "type": {"type": "string", "const": "columns_range"}}}, "contentPageComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"component_space_modifiers": {"$ref": "#/definitions/property/property3"}, "layout": {"$ref": "#/definitions/property/property1"}, "type": {"type": "string", "const": "content_page"}}}, "contentPageCategoryHeaderComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"component_space_modifiers": {"$ref": "#/definitions/property/property3"}, "layout": {"$ref": "#/definitions/property/property1"}, "type": {"type": "string", "const": "content_page_category_header"}}}, "contentPageCategoryResultsComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"component_space_modifiers": {"$ref": "#/definitions/property/property3"}, "has_content_pages_with_image": {"type": ["boolean", "null"], "default": null}, "layout": {"$ref": "#/definitions/property/property1"}, "max_level": {"type": ["integer", "null"], "default": null, "minimum": 0}, "title": {"type": ["object", "null"], "default": null, "additionalProperties": false, "required": ["text"], "properties": {"highlight": {"type": ["string", "null"], "default": null}, "text": {"type": "string"}}}, "type": {"type": "string", "const": "content_page_category_results"}}}, "contentPageContinueReadingComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"component_space_modifiers": {"$ref": "#/definitions/property/property3"}, "layout": {"$ref": "#/definitions/property/property1"}, "type": {"type": "string", "const": "content_page_continue_reading"}}}, "contentPageExcerptComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"component_space_modifiers": {"$ref": "#/definitions/property/property3"}, "layout": {"$ref": "#/definitions/property/property1"}, "max_length": {"type": ["integer", "null"], "default": null, "exclusiveMinimum": 0}, "split_on_line_end": {"type": "boolean", "default": false}, "start_after_length": {"type": ["integer", "null"], "default": null, "minimum": 1}, "type": {"type": "string", "const": "content_page_excerpt"}}}, "contentPageFooterComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"component_space_modifiers": {"$ref": "#/definitions/property/property3"}, "layout": {"$ref": "#/definitions/property/property1"}, "type": {"type": "string", "const": "content_page_footer"}}}, "contentPageHeaderComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"component_space_modifiers": {"$ref": "#/definitions/property/property3"}, "layout": {"$ref": "#/definitions/property/property1"}, "show_page_number": {"type": "boolean", "default": true}, "show_read_time": {"type": "boolean", "default": true}, "type": {"type": "string", "const": "content_page_header"}}}, "contentPageImageComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"align": {"$ref": "#/definitions/property/property7"}, "component_space_modifiers": {"$ref": "#/definitions/property/property3"}, "fallback_image": {"type": ["object", "null"], "$ref": "#/definitions/components/imageComponent"}, "layout": {"$ref": "#/definitions/property/property8"}, "show_caption": {"type": "boolean", "default": false}, "type": {"type": "string", "const": "content_page_image"}}}, "contentPageParagraphComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"component_space_modifiers": {"$ref": "#/definitions/property/property3"}, "layout": {"$ref": "#/definitions/property/property1"}, "max_length": {"type": ["integer", "null"], "default": null, "exclusiveMinimum": 0}, "split_on_line_end": {"type": "boolean", "default": false}, "start_after_length": {"type": ["integer", "null"], "default": null, "exclusiveMinimum": 0}, "type": {"type": "string", "const": "content_page_paragraph"}}}, "contentPageParagraphsComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"amount": {"type": ["integer", "null"], "default": null, "exclusiveMinimum": 0}, "component_space_modifiers": {"$ref": "#/definitions/property/property3"}, "layout": {"$ref": "#/definitions/property/property9"}, "paragraph_component": {"type": ["object", "null"], "$ref": "#/definitions/components/contentPageParagraphComponent"}, "type": {"type": "string", "const": "content_page_paragraphs"}}}, "contentPageResultsComponent": {"additionalProperties": false, "type": "object", "required": ["amount"], "properties": {"amount": {"type": "integer", "minimum": 1}, "amount_in_row": {"type": "integer", "default": 4, "minimum": 1, "maximum": 4}, "component_space_modifiers": {"$ref": "#/definitions/property/property3"}, "ignore_query": {"type": "boolean", "default": false}, "layout": {"$ref": "#/definitions/property/property10"}, "reset_counter": {"type": "boolean", "default": true}, "result_amount_optimization": {"type": "boolean", "default": true}, "title_component": {"type": ["object", "null"], "$ref": "#/definitions/components/titleComponent"}, "type": {"type": "string", "const": "content_page_results"}}}, "contentPageResultsAsOrganicResultsComponent": {"additionalProperties": false, "type": "object", "required": ["amount"], "properties": {"amount": {"type": "integer", "exclusiveMinimum": 0}, "component_space_modifiers": {"$ref": "#/definitions/property/property3"}, "layout": {"$ref": "#/definitions/property/property11"}, "link_to_active_brand": {"type": ["boolean", "null"], "default": false}, "max_description_length": {"type": ["integer", "null"], "default": null, "exclusiveMinimum": 0}, "result_amount_optimization": {"type": "boolean", "default": true}, "result_description_more_link": {"type": "boolean", "default": false}, "result_display_url_link": {"type": "boolean", "default": false}, "result_title_link": {"type": "boolean", "default": true}, "show_result_display_url": {"type": "boolean", "default": true}, "type": {"type": "string", "const": "content_page_results_as_organic_results"}}}, "contentPageSpotlightComponent": {"additionalProperties": false, "type": "object", "required": ["additional_amount"], "properties": {"additional_amount": {"type": "integer", "exclusiveMinimum": 0}, "component_space_modifiers": {"$ref": "#/definitions/property/property3"}, "layout": {"$ref": "#/definitions/property/property1"}, "title": {"type": ["object", "null"], "default": null, "additionalProperties": false, "required": ["text"], "properties": {"highlight": {"type": ["string", "null"], "default": null}, "text": {"type": "string"}}}, "type": {"type": "string", "const": "content_page_spotlight"}}}, "contentPageTitleComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"component_space_modifiers": {"$ref": "#/definitions/property/property3"}, "layout": {"$ref": "#/definitions/property/property12"}, "type": {"type": "string", "const": "content_page_title"}}}, "currentPageMatchesComponent": {"additionalProperties": false, "type": "object", "required": ["page"], "properties": {"no": {"type": "array", "items": {"$ref": "#/definitions/component"}}, "page": {"type": "integer", "exclusiveMinimum": 0}, "type": {"type": "string", "const": "current_page_matches"}, "yes": {"type": "array", "items": {"$ref": "#/definitions/component"}}}}, "disclaimerComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"component_space_modifiers": {"$ref": "#/definitions/property/property3"}, "layout": {"$ref": "#/definitions/property/property13"}, "type": {"type": "string", "const": "disclaimer"}}}, "displayBannerComponent": {"additionalProperties": false, "type": "object", "required": ["formats"], "properties": {"ad_unit_path": {"type": ["string", "null"], "default": null, "pattern": "^([a-z][a-z0-9-]*[a-z0-9])$"}, "formats": {"type": "array", "minItems": 1, "items": {"type": "object", "additionalProperties": false, "required": ["height", "width"], "properties": {"height": {"type": "integer", "exclusiveMinimum": 0}, "width": {"type": "integer", "exclusiveMinimum": 0}}}}, "layout": {"$ref": "#/definitions/property/property1"}, "type": {"type": "string", "const": "display_banner"}}}, "dynamicAdsComponent": {"additionalProperties": false, "type": "object", "required": ["amount", "unit"], "properties": {"amount": {"type": "integer", "exclusiveMinimum": 0}, "container_suffix": {"type": ["string", "null"], "default": null, "pattern": "^[a-z]{1}[a-z-0-9]+$"}, "repeated": {"type": "integer", "default": null, "minimum": 0}, "type": {"type": "string", "const": "dynamic_ads"}, "unit": {"$ref": "#/definitions/property/property14"}}}, "footerComponent": {"additionalProperties": false, "type": "object", "required": ["components"], "properties": {"components": {"type": "array", "items": {"$ref": "#/definitions/component"}}, "layout": {"$ref": "#/definitions/property/property1"}, "type": {"type": "string", "const": "footer"}}}, "footerLogoComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"hide_on_desktop": {"type": "boolean", "default": true}, "layout": {"$ref": "#/definitions/property/property1"}, "logo_dark_mode": {"type": "boolean", "default": false}, "logo_style_filter": {"$ref": "#/definitions/property/property15"}, "type": {"type": "string", "const": "footer_logo"}}}, "footerNavigationComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"component_space_modifiers": {"$ref": "#/definitions/property/property3"}, "layout": {"$ref": "#/definitions/property/property16"}, "logo_dark_mode": {"type": "boolean", "default": false}, "show_about": {"type": "boolean", "default": true}, "show_contact": {"type": "boolean", "default": true}, "show_copyright": {"type": "boolean", "default": true}, "show_disclaimer": {"type": "boolean", "default": true}, "show_privacy": {"type": "boolean", "default": true}, "type": {"type": "string", "const": "footer_navigation"}}}, "googleAdsBottomUnitComponent": {"additionalProperties": false, "type": "object", "required": ["amount"], "properties": {"amount": {"type": "integer", "exclusiveMinimum": 0}, "container_suffix": {"type": ["string", "null"], "default": null, "pattern": "^[a-z]{1}[a-z-0-9]+$"}, "repeated": {"type": "integer", "default": 0, "minimum": 0}, "type": {"type": "string", "const": "google_ads_bottom_unit"}}}, "googleAdsTopUnitComponent": {"additionalProperties": false, "type": "object", "required": ["amount"], "properties": {"amount": {"type": "integer", "exclusiveMinimum": 0}, "container_suffix": {"type": ["string", "null"], "default": null, "pattern": "^[a-z]{1}[a-z-0-9]+$"}, "type": {"type": "string", "const": "google_ads_top_unit"}}}, "googleRelatedTermsComponent": {"additionalProperties": false, "type": "object", "required": ["amount", "target"], "properties": {"amount": {"type": "integer", "minimum": 3, "maximum": 50}, "container_suffix": {"type": ["string", "null"], "default": null, "pattern": "^[a-z]{1}[a-z-0-9]+$"}, "fallback_related_terms": {"type": ["object", "null"], "$ref": "#/definitions/components/relatedTermsComponent"}, "route": {"type": ["string", "null"], "default": null, "pattern": "^route_[a-z][a-z_]+[a-z]$"}, "target": {"$ref": "#/definitions/property/property17"}, "terms_url_parameter_enabled": {"type": "boolean", "default": false}, "type": {"type": "string", "const": "google_related_terms"}}}, "groupComponent": {"additionalProperties": false, "type": "object", "required": ["layout"], "properties": {"layout": {"$ref": "#/definitions/property/property18"}, "type": {"type": "string", "const": "group"}}}, "hasBingAdsComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"layout": {"$ref": "#/definitions/property/property1"}, "no": {"type": "array", "items": {"$ref": "#/definitions/component"}}, "type": {"type": "string", "const": "has_bing_ads"}, "yes": {"type": "array", "items": {"$ref": "#/definitions/component"}}}}, "hasContentPageComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"no": {"type": "array", "items": {"$ref": "#/definitions/component"}}, "type": {"type": "string", "const": "has_content_page"}, "yes": {"type": "array", "items": {"$ref": "#/definitions/component"}}}}, "hasOrganicResultsComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"no": {"type": "array", "items": {"$ref": "#/definitions/component"}}, "type": {"type": "string", "const": "has_organic_results"}, "yes": {"type": "array", "items": {"$ref": "#/definitions/component"}}}}, "hasRelatedTermsComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"no": {"type": "array", "items": {"$ref": "#/definitions/component"}}, "type": {"type": "string", "const": "has_related_terms"}, "yes": {"type": "array", "items": {"$ref": "#/definitions/component"}}}}, "imageComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"component_space_modifiers": {"$ref": "#/definitions/property/property3"}, "image": {"$ref": "#/definitions/property/property19"}, "layout": {"$ref": "#/definitions/property/property1"}, "type": {"type": "string", "const": "image"}}}, "imageErrorMessageComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"type": {"type": "string", "const": "image_error_message"}}}, "imageFiltersComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"type": {"type": "string", "const": "image_filters"}}}, "imageResultsComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"layout": {"$ref": "#/definitions/property/property1"}, "type": {"type": "string", "const": "image_results"}}}, "infoPageComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"content": {"$ref": "#/definitions/property/property20"}, "layout": {"$ref": "#/definitions/property/property21"}, "type": {"type": "string", "const": "info_page"}}}, "infoPageMenuComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"layout": {"$ref": "#/definitions/property/property1"}, "type": {"type": "string", "const": "info_page_menu"}}}, "isAdBotComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"no": {"type": "array", "items": {"$ref": "#/definitions/component"}}, "type": {"type": "string", "const": "is_ad_bot"}, "yes": {"type": "array", "items": {"$ref": "#/definitions/component"}}}}, "jumbotronComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"component_space_modifiers": {"$ref": "#/definitions/property/property3"}, "layout": {"$ref": "#/definitions/property/property22"}, "logo_dark_mode": {"type": "boolean", "default": false}, "type": {"type": "string", "const": "jumbotron"}}}, "newsErrorMessageComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"type": {"type": "string", "const": "news_error_message"}}}, "newsFiltersComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"type": {"type": "string", "const": "news_filters"}}}, "newsResultsComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"layout": {"$ref": "#/definitions/property/property1"}, "type": {"type": "string", "const": "news_results"}}}, "organicContentPageResultsComponent": {"additionalProperties": false, "type": "object", "required": ["amount"], "properties": {"amount": {"type": "integer", "exclusiveMinimum": 0}, "component_space_modifiers": {"$ref": "#/definitions/property/property3"}, "layout": {"$ref": "#/definitions/property/property23"}, "link_to_active_brand": {"type": ["boolean", "null"], "default": false}, "max_description_length": {"type": ["integer", "null"], "default": null, "exclusiveMinimum": 0}, "result_amount_optimization": {"type": "boolean", "default": true}, "result_description_more_link": {"type": "boolean", "default": false}, "result_display_url_link": {"type": "boolean", "default": false}, "result_image_link": {"type": "boolean", "default": true}, "result_title_link": {"type": "boolean", "default": true}, "show_result_display_url": {"type": "boolean", "default": true}, "type": {"type": "string", "const": "organic_content_page_results"}}}, "organicErrorMessageComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"type": {"type": "string", "const": "organic_error_message"}}}, "organicPaginationComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"layout": {"$ref": "#/definitions/property/property24"}, "type": {"type": "string", "const": "organic_pagination"}}}, "organicResultsComponent": {"additionalProperties": false, "type": "object", "required": ["amount"], "properties": {"amount": {"type": "integer", "exclusiveMinimum": 0}, "component_space_modifiers": {"$ref": "#/definitions/property/property3"}, "layout": {"$ref": "#/definitions/property/property11"}, "max_description_length": {"type": ["integer", "null"], "default": null, "exclusiveMinimum": 0}, "result_amount_optimization": {"type": "boolean", "default": true}, "result_description_more_link": {"type": "boolean", "default": false}, "result_display_url_link": {"type": "boolean", "default": false}, "result_title_link": {"type": "boolean", "default": true}, "show_result_display_url": {"type": "boolean", "default": true}, "type": {"type": "string", "const": "organic_results"}}}, "organicResultsTitleComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"component_space_modifiers": {"$ref": "#/definitions/property/property25"}, "layout": {"$ref": "#/definitions/property/property26"}, "show_query": {"type": "boolean", "default": true}, "type": {"type": "string", "const": "organic_results_title"}}}, "organicResultsWithFallbackComponent": {"additionalProperties": false, "type": "object", "required": ["fallback", "results"], "properties": {"fallback": {"type": "object", "anyOf": [{"$ref": "#/definitions/components/contentPageResultsComponent"}, {"$ref": "#/definitions/components/contentPageResultsAsOrganicResultsComponent"}, {"$ref": "#/definitions/components/contentPageSpotlightComponent"}, {"$ref": "#/definitions/components/organicContentPageResultsComponent"}, {"$ref": "#/definitions/components/organicResultsComponent"}]}, "results": {"type": "object", "anyOf": [{"$ref": "#/definitions/components/contentPageResultsComponent"}, {"$ref": "#/definitions/components/contentPageResultsAsOrganicResultsComponent"}, {"$ref": "#/definitions/components/contentPageSpotlightComponent"}, {"$ref": "#/definitions/components/organicContentPageResultsComponent"}, {"$ref": "#/definitions/components/organicResultsComponent"}]}, "type": {"type": "string", "const": "organic_results_with_fallback"}}}, "pageNotFoundErrorMessageComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"layout": {"$ref": "#/definitions/property/property1"}, "type": {"type": "string", "const": "page_not_found_error_message"}}}, "pillRelatedTermsComponent": {"additionalProperties": false, "type": "object", "required": ["amount"], "properties": {"amount": {"type": "integer", "exclusiveMinimum": 0}, "component_space_modifiers": {"$ref": "#/definitions/property/property3"}, "layout": {"$ref": "#/definitions/property/property27"}, "type": {"type": "string", "const": "pill_related_terms"}}}, "preferencesComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"layout": {"$ref": "#/definitions/property/property1"}, "type": {"type": "string", "const": "preferences"}}}, "relatedSourceMatchesVisymoComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"no": {"type": "array", "items": {"$ref": "#/definitions/component"}}, "type": {"type": "string", "const": "related_source_matches_visymo"}, "yes": {"type": "array", "items": {"$ref": "#/definitions/component"}}}}, "relatedTermsComponent": {"additionalProperties": false, "type": "object", "required": ["amount"], "properties": {"amount": {"type": "integer", "exclusiveMinimum": 0}, "columns": {"type": "integer", "default": 2, "minimum": 1, "maximum": 20}, "component_space_modifiers": {"$ref": "#/definitions/property/property3"}, "keyword_highlight": {"type": ["boolean", "null"], "default": null}, "layout": {"$ref": "#/definitions/property/property28"}, "repeat_terms": {"type": "boolean", "default": true}, "route": {"type": ["string", "null"], "default": null, "pattern": "^route_[a-z][a-z_]+[a-z]$"}, "show_title": {"type": "boolean", "default": true}, "type": {"type": "string", "const": "related_terms"}, "zone": {"$ref": "#/definitions/property/property29"}}}, "scrollToTopComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"layout": {"$ref": "#/definitions/property/property1"}, "type": {"type": "string", "const": "scroll_to_top"}}}, "searchBarComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"allow_start_query_search": {"type": "boolean", "default": true}, "autofocus": {"type": "boolean", "default": false}, "component_space_modifiers": {"$ref": "#/definitions/property/property3"}, "layout": {"$ref": "#/definitions/property/property30"}, "show_search_query": {"type": "boolean", "default": true}, "type": {"type": "string", "const": "search_bar"}}}, "searchHeaderComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"autofocus": {"type": "boolean", "default": false}, "component_space_modifiers": {"$ref": "#/definitions/property/property3"}, "layout": {"$ref": "#/definitions/property/property31"}, "logo_dark_mode": {"type": "boolean", "default": false}, "logo_style_filter": {"$ref": "#/definitions/property/property15"}, "show_background_on_desktop": {"type": "boolean", "default": false}, "show_background_on_mobile": {"type": "boolean", "default": false}, "show_background_on_tablet": {"type": "boolean", "default": false}, "show_hamburger_menu_on_desktop": {"type": "boolean", "default": false}, "show_hamburger_menu_on_mobile": {"type": "boolean", "default": false}, "show_hamburger_menu_on_tablet": {"type": "boolean", "default": false}, "show_main_menu_more_on_desktop": {"type": "boolean", "default": false}, "show_main_menu_more_on_mobile": {"type": "boolean", "default": false}, "show_main_menu_more_on_tablet": {"type": "boolean", "default": false}, "show_main_menu_on_desktop": {"type": "boolean", "default": false}, "show_main_menu_on_mobile": {"type": "boolean", "default": false}, "show_main_menu_on_tablet": {"type": "boolean", "default": false}, "show_search_query": {"type": "boolean", "default": true}, "show_sub_menu_on_desktop": {"type": "boolean", "default": false}, "show_sub_menu_on_mobile": {"type": "boolean", "default": false}, "show_sub_menu_on_tablet": {"type": "boolean", "default": false}, "type": {"type": "string", "const": "search_header"}}}, "sharePageComponent": {"additionalProperties": false, "type": "object", "required": ["share"], "properties": {"component_space_modifiers": {"$ref": "#/definitions/property/property3"}, "layout": {"$ref": "#/definitions/property/property1"}, "share": {"$ref": "#/definitions/property/property32"}, "type": {"type": "string", "const": "share_page"}}}, "sloganComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"component_space_modifiers": {"$ref": "#/definitions/property/property3"}, "layout": {"$ref": "#/definitions/property/property33"}, "translation_id": {"$ref": "#/definitions/property/property34"}, "type": {"type": "string", "const": "slogan"}}}, "splitTestMatchesComponent": {"additionalProperties": false, "type": "object", "required": ["one_of_variants"], "properties": {"description": {"type": ["string", "null"], "default": null}, "no": {"type": "array", "items": {"$ref": "#/definitions/component"}}, "one_of_variants": {"type": "array", "items": {"type": "string"}, "minItems": 1}, "type": {"type": "string", "const": "split_test_matches"}, "yes": {"type": "array", "items": {"$ref": "#/definitions/component"}}}}, "startPageComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"layout": {"$ref": "#/definitions/property/property1"}, "type": {"type": "string", "const": "start_page"}}}, "startPageFavoritesComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"layout": {"$ref": "#/definitions/property/property1"}, "type": {"type": "string", "const": "start_page_favorites"}}}, "tableOfContentsComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"component_space_modifiers": {"$ref": "#/definitions/property/property3"}, "layout": {"$ref": "#/definitions/property/property1"}, "type": {"type": "string", "const": "table_of_contents"}}}, "titleComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"component_space_modifiers": {"$ref": "#/definitions/property/property3"}, "layout": {"$ref": "#/definitions/property/property35"}, "subtitle_translation_id": {"$ref": "#/definitions/property/property36"}, "title": {"type": ["string", "null"], "default": null}, "title_highlight": {"type": ["string", "null"], "default": null}, "title_highlight_translation_id": {"$ref": "#/definitions/property/property36"}, "translation_id": {"$ref": "#/definitions/property/property36"}, "type": {"type": "string", "const": "title"}}}, "unexpectedErrorMessageComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"layout": {"$ref": "#/definitions/property/property1"}, "type": {"type": "string", "const": "unexpected_error_message"}}}, "webSearchStatsTitleComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"layout": {"$ref": "#/definitions/property/property1"}, "show_random_stats": {"type": "boolean", "default": false}, "type": {"type": "string", "const": "web_search_stats_title"}}}}}, "required": ["components"], "properties": {"components": {"additionalProperties": false, "default": [], "items": {"$ref": "#/definitions/component"}, "type": "array"}}}