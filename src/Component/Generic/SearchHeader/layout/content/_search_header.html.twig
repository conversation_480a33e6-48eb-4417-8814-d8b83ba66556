{# @var layout string #}
{# @var logo string|null #}
{# @var logo_style_filter App\Generic\Logo\LogoStyleFilter|null #}
{# @var query string #}
{# @var show_categories bool #}
{# @var layout_variant string #}
{{ component_javascript(['searchHeader', 'searchHeaderContentCategory']) }}
{% set component_class = 'search-header' %}
{% set show_categories = show_categories|default(false) %}
{% set layout_variant = layout_variant|default('basic') %}

{# Determine layout modifier based on variant #}
{% set layout_modifier = layout %}
{% if layout_variant == 'category-left-logo' %}
    {% set layout_modifier = 'content-category-1' %}
{% elseif layout_variant == 'category-right-logo' %}
    {% set layout_modifier = 'content-category-2' %}
{% endif %}

<div class="{{ component_class(component_class, [layout_modifier]) }}"{{ delayed_container_attributes() }}>
    {# Logo section #}
    <div class="{{ component_class }}__logo">
        <a href="{{ persistent_new_search_path('route_home') }}" class="{{ component_class }}__brand-link">
            <img src="{{ brand_image_base64(logo) }}" alt="{{ brand_name }}" class="{{ component_class }}__brand-image {{ render_logo_class(logo_style_filter) }}"/>
            <img src="{{ brand_image_base64('favicon.png') }}" alt="{{ brand_name }}" class="{{ component_class }}__brand-icon {{ render_logo_class(logo_style_filter) }}"/>
        </a>
    </div>
    
    {# Categories navigation (only shown when show_categories is true) #}
    {% if show_categories %}
        <div class="{{ component_class }}__navigation">
            <ul class="{{ component_class }}__categories">
                {% for category in content_page_categories %}
                    <li class="{{ component_class }}__category">
                        <a href="{{ generate_category_url(category) }}" class="{{ component_class }}__category-link">{{ category.title }}</a>
                    </li>
                {% endfor %}
            </ul>
        </div>
        <div class="{{ component_class }}__navigation-more">
            <span class="{{ component_class }}__category-more vsi">{{ 'search_header.more_categories'|trans }}</span>
        </div>
    {% endif %}
    
    {# Search bar #}
    {{ search_bar|raw }}
</div>
