@import "../default/searchHeaderMixins";

/** @define search-header */
// stylelint-disable visymo/sort-properties-alphabetically

// Base styles for all content layouts
%search-header-content-base {
    position: relative;
    padding-bottom: 1.5rem;
    padding-top: 1.5rem;

    @include search-header-search-bar-mobile-full-page;

    .search-header {
        // Brand logo
        &__logo {
            align-self: center;
            grid-area: logo;
        }

        &__brand-image {
            height: max-content;
            max-height: 4.4rem;
            max-width: 20rem;
            object-fit: scale-down;
        }

        &__brand-icon {
            display: none;
        }

        &__brand-link {
            display: inline-block;
        }

        // Navigation (for category layouts)
        &__navigation {
            align-self: center;
            grid-area: navigation;
            height: 4rem;
            line-height: 4rem;
            overflow: hidden;
            padding-right: 0.4rem;
        }

        &__categories {
            font-size: 1.6rem;
            font-weight: 700;
        }

        &__category-link {
            color: var(--container__section_color, #000000);
            white-space: nowrap;

            &:hover {
                text-decoration: underline;
            }
        }

        &__category-more {
            color: var(--container__section_color, #000000);
            cursor: pointer;
            display: block;
            font-size: 1.6rem;
            font-weight: 700;
            line-height: 4rem;
            padding-right: 2rem;
            position: relative;
            user-select: none;

            &::before {
                color: var(--container__section_color, #555555);
                content: $vsi-chevron-down;
                position: absolute;
                right: 0;
                transition: 0.2s;
            }
        }

        &__navigation-more {
            /* stylelint-disable-next-line selector-class-pattern */
            &--active .search-header__category-more::before {
                transform: rotate(180deg);
            }
        }

        // Auto suggest
        &__auto-suggest {
            --border-color: #dddddd;
            top: 100%;
        }
    }
}

// Basic content layout (no categories)
.search-header--content,
.search-header--content-1 {
    @extend %search-header-content-base;

    display: grid;
    column-gap: 1rem;
    grid-template-areas: "logo search";

    .search-header {
        &__navigation,
        &__navigation-more {
            display: none;
        }
    }

    // Device responsive specific
    @media #{map-get($media-max, b)} {
        .search-header {
            // Switch brand logo with favicon
            &__brand-image {
                display: none;
            }

            &__brand-icon {
                display: block;
            }
        }
    }

    /* stylelint-disable-next-line plugin/selector-bem-pattern */
    .search-bar--default {
        --padding: 0;
        --field-button_font-size: var(--field-button-icon_font-size, 1.4rem);
        --field-button_width: 5rem;
        --field_box-shadow: none;
        grid-area: search;
        max-width: 68rem;
        place-self: center end;
        width: 100%;

        @media #{map-get($media-max, c)} {
            // stylelint-disable selector-class-pattern
            // stylelint-disable plugin/selector-bem-pattern
            .search-bar__field-button {
                background-color: var(--field-button_background-color, var(--brand-primary-color));
                color: var(--field-button-highlight_color, #ffffff);
            }

            // stylelint-enable selector-class-pattern
            // stylelint-enable plugin/selector-bem-pattern
        }
    }
}

// Category layout 1 (logo left, categories center, search right)
.search-header--content-category-1 {
    @extend %search-header-content-base;

    display: grid;

    // Device responsive specific
    @media #{map-get($media-min, c)} {
        column-gap: 1.5rem;
        grid-template-areas: "logo navigation navigation-more search";
        grid-template-columns: auto minmax(0, 1fr) auto 27rem;

        .search-header {
            &__categories {
                column-gap: 1.5rem;
                display: flex;
                flex-shrink: 1;
                flex-wrap: wrap;
                justify-content: flex-end;
            }

            &__category-more {
                margin-right: 1.5rem;
            }
        }
    }

    // Device responsive specific
    @media #{map-get($media-max, b)} {
        grid-template-areas: "logo" "search" "navigation" "navigation-more";
        grid-template-columns: auto;

        .search-header {
            &__logo {
                justify-self: center;
            }

            &__form {
                margin: 1rem 0;
                max-width: none;
            }

            &__navigation {
                height: auto;
                overflow: visible;
            }

            &__categories {
                display: block;
                font-size: 1.6rem;
                font-weight: 700;
                justify-content: flex-start;
            }
        }
    }

    /* stylelint-disable-next-line plugin/selector-bem-pattern */
    .search-bar--default {
        grid-area: search;
        place-self: center end;
        width: 100%;

        --field_box-shadow: 0 0.1rem 0.4rem rgba(20, 23, 26, 0.1);
        --field-button_font-size: var(--field-button-icon_font-size, 1.4rem);
        --field-button_width: 4rem;
        --field-corner_border-radius: 0.8rem;
        --field-button-icon_font-size: 1.8rem;
        --field-button-icon_font-weight: 400;
        --padding: 0;

        @include search-header-auto-suggest;
    }
}

// Category layout 2 (search left, categories center, logo right)
.search-header--content-category-2 {
    @extend %search-header-content-base;

    display: grid;

    // Device responsive specific
    @media #{map-get($media-min, c)} {
        column-gap: 1.5rem;
        grid-template-areas: "search navigation navigation-more logo";
        grid-template-columns: 27rem minmax(0, 1fr) auto auto;

        .search-header {
            &__categories {
                column-gap: 1.5rem;
                display: flex;
                flex-shrink: 1;
                flex-wrap: wrap;
                justify-content: flex-end;
            }

            &__category-more {
                margin-right: 1.5rem;
            }
        }
    }

    // Device responsive specific
    @media #{map-get($media-max, b)} {
        grid-template-areas: "logo" "search" "navigation" "navigation-more";
        grid-template-columns: auto;

        .search-header {
            &__logo {
                justify-self: center;
            }

            &__form {
                margin: 1rem 0;
                max-width: none;
            }

            &__navigation {
                height: auto;
                overflow: visible;
            }

            &__categories {
                display: block;
                font-size: 1.6rem;
                font-weight: 700;
                justify-content: flex-start;
            }
        }
    }

    /* stylelint-disable-next-line plugin/selector-bem-pattern */
    .search-bar--default {
        grid-area: search;

        --field_box-shadow: 0 0.1rem 0.4rem rgba(20, 23, 26, 0.1);
        --field-button_font-size: var(--field-button-icon_font-size, 1.4rem);
        --field-button_width: 4rem;
        --field-corner_border-radius: 0.8rem;
        --field-button-icon_font-size: 1.8rem;
        --field-button-icon_font-weight: 400;
        --padding: 0;

        @include search-header-auto-suggest;
    }
}
