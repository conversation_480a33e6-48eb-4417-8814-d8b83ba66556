<?php

declare(strict_types=1);

namespace App\Component\Generic\SearchHeader;

use App\BrandAssets\File\BrandAssetsImageFileName;
use App\JsonTemplate\Component\Layout\LayoutInterface;
use App\JsonTemplate\Component\Layout\LogoLayoutInterface;

// phpcs:disable Generic.Files.LineLength.MaxExceeded
enum SearchHeaderLayout: string implements LayoutInterface, LogoLayoutInterface
{
    case CONTENT          = 'content';
    case CONTENT_1          = 'content-1';
    case CONTENT_CATEGORY_1 = 'content-category-1';
    case CONTENT_CATEGORY_2 = 'content-category-2';
    case DEFAULT            = 'default';
    case HOME               = 'home';
    case HOME_ROUNDED       = 'home_rounded';
    case ROUNDED            = 'rounded';
    case STARTPAGE          = 'startpage';
    case VISYMO             = 'visymo';

    public static function getDefault(): self
    {
        return self::DEFAULT;
    }

    public function getTwigTemplate(): string
    {
        return match ($this) {
            self::CONTENT            => '@component/Generic/SearchHeader/layout/content/search_header-content.html.twig',
            self::CONTENT_1          => '@component/Generic/SearchHeader/layout/content1/search_header-content_1.html.twig',
            self::CONTENT_CATEGORY_1 => '@component/Generic/SearchHeader/layout/contentCategory1/search_header-content_category_1.html.twig',
            self::CONTENT_CATEGORY_2 => '@component/Generic/SearchHeader/layout/contentCategory2/search_header-content_category_2.html.twig',
            self::DEFAULT            => '@component/Generic/SearchHeader/layout/default/search_header-default.html.twig',
            self::HOME               => '@component/Generic/SearchHeader/layout/home/<USER>',
            self::HOME_ROUNDED       => '@component/Generic/SearchHeader/layout/homeRounded/search_header-home_rounded.html.twig',
            self::ROUNDED            => '@component/Generic/SearchHeader/layout/rounded/search_header-rounded.html.twig',
            self::STARTPAGE          => '@component/Generic/SearchHeader/layout/startpage/search_header-startpage.html.twig',
            self::VISYMO             => '@component/Generic/SearchHeader/layout/visymo/search_header-visymo.html.twig',
        };
    }

    public function requiresContentPageCategories(): bool
    {
        return match ($this) {
            self::CONTENT_CATEGORY_1,
            self::CONTENT_CATEGORY_2 => true,
            default                  => false,
        };
    }

    public function getLogo(bool $darkMode): ?BrandAssetsImageFileName
    {
        if ($darkMode) {
            return match ($this) {
                self::CONTENT_1,
                self::CONTENT_CATEGORY_1,
                self::CONTENT_CATEGORY_2,
                self::DEFAULT,
                self::ROUNDED,
                self::STARTPAGE,
                self::VISYMO => BrandAssetsImageFileName::LOGO_SMALL_DARK_MODE_PNG,
                self::HOME_ROUNDED,
                self::HOME   => null,
            };
        }

        return match ($this) {
            self::CONTENT_1,
            self::CONTENT_CATEGORY_1,
            self::CONTENT_CATEGORY_2,
            self::DEFAULT,
            self::ROUNDED,
            self::STARTPAGE,
            self::VISYMO => BrandAssetsImageFileName::LOGO_SMALL_PNG,
            self::HOME_ROUNDED,
            self::HOME   => null,
        };
    }

    public function getShowFavicon(): bool
    {
        return match ($this) {
            self::STARTPAGE,
            self::CONTENT_1,
            self::CONTENT_CATEGORY_1,
            self::CONTENT_CATEGORY_2 => true,
            default                  => false,
        };
    }
}
