<?php

declare(strict_types=1);

namespace App\Component\Generic\SearchHeaderContent;

use App\Component\Generic\AbstractSpace\AbstractSpaceComponent;
use App\Generic\Logo\LogoStyleFilter;

final class SearchHeaderContentComponent extends AbstractSpaceComponent
{
    /**
     * @param string[] $componentSpaceModifiers
     */
    public function __construct(
        public readonly SearchHeaderContentLayout $layout,
        public readonly SearchHeaderContentLogoPosition $logoPosition,
        public readonly bool $showCategories,
        public readonly bool $autofocus,
        public readonly bool $logoDarkMode,
        public readonly ?LogoStyleFilter $logoStyleFilter,
        public readonly bool $showSearchQuery,
        array $componentSpaceModifiers
    )
    {
        parent::__construct($componentSpaceModifiers);
    }

    public static function getType(): string
    {
        return 'search_header_content';
    }

    public function getRenderer(): string
    {
        return SearchHeaderContentRenderer::class;
    }
}
