<?php

declare(strict_types=1);

namespace App\Component\Generic\SearchHeaderContent;

use App\Brand\Settings\BrandSettingsHelper;
use App\BrandSettings\Helper\BrandSettingsHelper;
use App\Component\ComponentInterface;
use App\Component\Exception\UnsupportedComponentException;
use App\Component\Generic\SearchBar\SearchBarRenderer;
use App\Component\Renderer\AbstractComponentRenderer;
use App\ContentPage\Helper\ContentPageCategoryHelper;
use App\ContentPage\Model\ContentPageNestedCategory;
use App\Generic\Menu\Helper\MenuHelper;
use App\Generic\SearchBar\SearchBarRenderer;
use App\JsonTemplate\Component\AbstractComponentRenderer;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\View\Data\ViewDataProperty;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\Menu\Helper\MenuHelper;
use App\Route\Registry\RouteRegistry;
use App\Search\Registry\RouteRegistry;
use App\SearchApi\Manager\SearchApiManager;
use App\SearchApi\SearchApiManager;
use App\SplitTest\Extended\SplitTestExtendedReader;
use App\View\ViewDataConditionCollection;
use App\View\ViewDataProperty;
use App\View\ViewDataRequest;
use App\View\ViewInterface;
use Twig\Environment;

final class SearchHeaderContentRenderer extends AbstractComponentRenderer
{
    private const MAX_CONTENT_PAGE_CATEGORIES = 6;

    public function __construct(
        private readonly Environment $twig,
        private readonly SearchApiManager $searchApiManager,
        private readonly SearchBarRenderer $searchBarRenderer,
        private readonly RouteRegistry $routeRegistry,
        private readonly BrandSettingsHelper $brandSettingsHelper,
        private readonly MenuHelper $menuHelper,
        private readonly ContentPageCategoryHelper $contentPageCategoryHelper,
        private readonly SplitTestExtendedReader $splitTestExtendedReader,
    )
    {
    }

    protected function registerDataRequirements(
        ComponentInterface $component,
        ViewDataRequest $request
    ): void
    {
        if (!$component instanceof SearchHeaderContentComponent) {
            throw UnsupportedComponentException::create($component, [SearchHeaderContentComponent::class]);
        }

        if ($component->showCategories) {
            $request
                ->setRequirements(
                    [
                        ViewDataProperty::CONTENT_PAGE_CATEGORIES,
                        ViewDataProperty::QUERY,
                    ],
                );
        } else {
            $request->setRequirements(
                [
                    ViewDataProperty::QUERY,
                ],
            );
        }
    }

    protected function buildRequest(
        ComponentInterface $component,
        ViewDataRequest $request,
        ViewDataConditionCollection $conditions
    ): void
    {
        if (!$component instanceof SearchHeaderContentComponent) {
            throw UnsupportedComponentException::create($component, [SearchHeaderContentComponent::class]);
        }

        if (!$component->showCategories) {
            return;
        }

        $contentPageCategoriesViewDataRequest = $request
            ->contentPageCategories()
            ->enable()
            ->setMaxLevel(0)
            ->setHasImage(true)
            ->setIsAdult(false);

        $this->searchApiManager->registerComponentSearchRequest(
            component               : $component,
            viewDataProperty        : ViewDataProperty::CONTENT_PAGE_CATEGORIES,
            searchApiViewDataRequest: $contentPageCategoriesViewDataRequest,
            conditions              : $conditions,
        );
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof SearchHeaderContentComponent) {
            throw UnsupportedComponentException::create($component, [SearchHeaderContentComponent::class]);
        }

        $darkModeLogo = $this->splitTestExtendedReader->isVariantActive('wsdm') || $component->logoDarkMode;

        return $this->twig->render(
            $component->layout->getTwigTemplate(),
            [
                'view'                    => $view,
                'query'                   => $component->showSearchQuery
                    ? $view->getDataRegistry()->getQuery()
                    : null,
                'layout'                  => $component->layout->value,
                'logo_position'           => $component->logoPosition->value,
                'show_categories'         => $component->showCategories,
                'autofocus'               => $component->autofocus,
                'content_page_categories' => $this->getContentPageCategories($component, $view),
                'logo'                    => $component->layout->getLogo($darkModeLogo)?->value,
                'logo_style_filter'       => $component->logoStyleFilter,
                'search_bar'              => $this->searchBarRenderer->render($component, $view),
                'search_route'            => $this->routeRegistry->getSearchRoute(),
                'brand_name'              => $this->brandSettingsHelper->getSettings()->getName(),
                'menu_helper'             => $this->menuHelper,
            ],
        );
    }

    /**
     * @return ContentPageNestedCategory[]
     */
    private function getContentPageCategories(SearchHeaderContentComponent $component, ViewInterface $view): array
    {
        if (!$component->showCategories) {
            return [];
        }

        return $this->contentPageCategoryHelper->getPreferenceContentPageCategories(
            $view->getDataRegistry()->getContentPageCategories($component)->results,
            self::MAX_CONTENT_PAGE_CATEGORIES,
        );
    }
}
