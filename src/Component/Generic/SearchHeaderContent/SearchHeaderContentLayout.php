<?php

declare(strict_types=1);

namespace App\Component\Generic\SearchHeaderContent;

use App\BrandAssets\File\BrandAssetsImageFileName;
use App\JsonTemplate\Component\Layout\LayoutInterface;
use App\JsonTemplate\Component\Layout\LogoLayoutInterface;

enum SearchHeaderContentLayout: string implements LayoutInterface, LogoLayoutInterface
{
    case DEFAULT = 'default';

    public static function getDefault(): self
    {
        return self::DEFAULT;
    }

    public function getTwigTemplate(): string
    {
        return match ($this) {
            self::DEFAULT => '@component/Generic/SearchHeaderContent/layout/default/search_header_content-default.html.twig',
        };
    }

    public function getLogo(bool $darkMode): ?BrandAssetsImageFileName
    {
        if ($darkMode) {
            return match ($this) {
                self::DEFAULT => BrandAssetsImageFileName::LOGO_SMALL_DARK_MODE_PNG,
            };
        }

        return match ($this) {
            self::DEFAULT => BrandAssetsImageFileName::LOGO_SMALL_PNG,
        };
    }

    public function getShowFavicon(): bool
    {
        return match ($this) {
            self::DEFAULT => true,
        };
    }
}
