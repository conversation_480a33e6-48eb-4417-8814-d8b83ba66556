appReady.push(function () {
    Helper.iterateHtmlElements('.search-header-content__form', function (searchFormElement) {
        var enableAutoSuggest = searchFormElement.querySelector('.auto-suggest') !== null;

        if (enableAutoSuggest) {
            (new AutoSuggest()).create(searchFormElement);
        } else {
            (new SearchBar()).create(searchFormElement, {
                preventMoveToStart: true
            });
        }
    });
});
