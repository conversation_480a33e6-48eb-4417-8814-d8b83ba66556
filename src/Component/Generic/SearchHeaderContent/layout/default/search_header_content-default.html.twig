{# @var layout string #}
{# @var logo_position string #}
{# @var logo string|null #}
{# @var logo_style_filter App\Generic\Logo\LogoStyleFilter|null #}
{# @var query string #}
{# @var show_categories bool #}
{{ component_javascript(['searchHeaderContent', 'searchHeaderContentCategory']) }}
{{ component_style('searchHeaderContent') }}

{% set component_class = 'search-header-content' %}
{% set show_categories = show_categories|default(false) %}
{% set logo_position = logo_position|default('left') %}

{# Determine layout modifier based on logo position and categories #}
{% set layout_modifier = 'default' %}
{% if show_categories %}
    {% if logo_position == 'left' %}
        {% set layout_modifier = 'category-left-logo' %}
    {% elseif logo_position == 'right' %}
        {% set layout_modifier = 'category-right-logo' %}
    {% endif %}
{% else %}
    {% set layout_modifier = 'basic' %}
{% endif %}

<div class="{{ component_class(component_class, [layout_modifier]) }}"{{ delayed_container_attributes() }}>
    {# Logo section #}
    {% if logo_position != 'none' %}
        <div class="{{ component_class }}__logo">
            <a href="{{ persistent_new_search_path('route_home') }}" class="{{ component_class }}__brand-link">
                <img src="{{ brand_image_base64(logo) }}" alt="{{ brand_name }}" class="{{ component_class }}__brand-image {{ render_logo_class(logo_style_filter) }}"/>
                <img src="{{ brand_image_base64('favicon.png') }}" alt="{{ brand_name }}" class="{{ component_class }}__brand-icon {{ render_logo_class(logo_style_filter) }}"/>
            </a>
        </div>
    {% endif %}
    
    {# Categories navigation (only shown when show_categories is true) #}
    {% if show_categories %}
        <div class="{{ component_class }}__navigation">
            <ul class="{{ component_class }}__categories">
                {% for category in content_page_categories %}
                    <li class="{{ component_class }}__category">
                        <a href="{{ generate_category_url(category) }}" class="{{ component_class }}__category-link">{{ category.title }}</a>
                    </li>
                {% endfor %}
            </ul>
        </div>
        <div class="{{ component_class }}__navigation-more">
            <span class="{{ component_class }}__category-more vsi">{{ 'search_header.more_categories'|trans }}</span>
        </div>
    {% endif %}
    
    {# Search bar #}
    {{ search_bar|raw }}
</div>
